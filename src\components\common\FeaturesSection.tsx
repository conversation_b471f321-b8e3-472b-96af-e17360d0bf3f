import React from 'react';
import { Card } from '../ui';
import { features } from '../../data';
import { cn } from '../../utils';

// Custom animations
const customAnimations = `
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
  }
  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(-5deg); }
  }
  @keyframes float-slow {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-15px) scale(1.05); }
  }
  @keyframes float-particle {
    0% { transform: translateY(0px) opacity(0.6); }
    50% { transform: translateY(-100px) opacity(1); }
    100% { transform: translateY(-200px) opacity(0); }
  }
  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  @keyframes bounce-slow {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  @keyframes glow-pulse {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6), 0 0 60px rgba(139, 92, 246, 0.3); }
  }
  .animate-float { animation: float 6s ease-in-out infinite; }
  .animate-float-delayed { animation: float-delayed 8s ease-in-out infinite; }
  .animate-float-slow { animation: float-slow 10s ease-in-out infinite; }
  .animate-float-particle { animation: float-particle linear infinite; }
  .animate-spin-slow { animation: spin-slow 20s linear infinite; }
  .animate-bounce-slow { animation: bounce-slow 4s ease-in-out infinite; }
  .animate-glow-pulse { animation: glow-pulse 3s ease-in-out infinite; }
`;

// Professional SVG Icon Components - clean and minimal
const TargetIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const BookIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M8 7h8M8 11h6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

const LightningIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/>
  </svg>
);

const ChartIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="3" y="12" width="4" height="9" fill="currentColor" fillOpacity="0.2" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <rect x="10" y="8" width="4" height="13" fill="currentColor" fillOpacity="0.15" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <rect x="17" y="4" width="4" height="17" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <path d="M3 3v18h18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const UsersIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="9" cy="7" r="4" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <circle cx="15" cy="7" r="4" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M1 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    <path d="M15 21v-2a4 4 0 0 1 4-4h4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const DeviceIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="5" y="2" width="14" height="20" rx="3" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <rect x="7" y="5" width="10" height="12" fill="white" stroke="currentColor" strokeWidth="1" rx="1"/>
    <circle cx="12" cy="19" r="1.5" fill="currentColor"/>
  </svg>
);

// Icon mapping
const iconMap: { [key: string]: React.ComponentType } = {
  '🎯': TargetIcon,
  '📚': BookIcon,
  '⚡': LightningIcon,
  '📊': ChartIcon,
  '👥': UsersIcon,
  '📱': DeviceIcon,
};

interface FeaturesSectionProps {
  className?: string;
}

export const FeaturesSection: React.FC<FeaturesSectionProps> = ({ className }) => {
  return (
    <section id="features" className={cn('relative py-32 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden', className)}>
      {/* Advanced Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated mesh gradient */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-pink-600/20 animate-pulse"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-tl from-cyan-600/20 via-blue-600/20 to-indigo-600/20 animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        {/* Floating orbs with glow */}
        <div className="absolute -top-32 -right-32 w-64 h-64 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full opacity-20 blur-3xl animate-float"></div>
        <div className="absolute -bottom-32 -left-32 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full opacity-15 blur-3xl animate-float-delayed"></div>
        <div className="absolute top-1/3 right-1/4 w-48 h-48 bg-gradient-to-br from-indigo-400 to-blue-400 rounded-full opacity-25 blur-2xl animate-float-slow"></div>

        {/* Geometric patterns */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-32 h-32 border-2 border-cyan-400 rounded-lg rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-20 right-20 w-24 h-24 border-2 border-purple-400 rounded-full animate-pulse"></div>
          <div className="absolute top-1/2 left-10 w-16 h-16 border border-blue-400 rotate-12 animate-bounce-slow"></div>
        </div>

        {/* Neural network lines */}
        <svg className="absolute inset-0 w-full h-full opacity-10" viewBox="0 0 1000 600">
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8"/>
              <stop offset="50%" stopColor="#8B5CF6" stopOpacity="0.6"/>
              <stop offset="100%" stopColor="#EC4899" stopOpacity="0.4"/>
            </linearGradient>
          </defs>
          <path d="M100,100 Q300,50 500,150 T900,100" stroke="url(#lineGradient)" strokeWidth="2" fill="none" className="animate-pulse"/>
          <path d="M50,300 Q250,200 450,350 T850,300" stroke="url(#lineGradient)" strokeWidth="1.5" fill="none" className="animate-pulse" style={{ animationDelay: '0.5s' }}/>
          <path d="M150,500 Q350,400 550,550 T950,500" stroke="url(#lineGradient)" strokeWidth="1" fill="none" className="animate-pulse" style={{ animationDelay: '1s' }}/>
        </svg>
      </div>

      {/* Floating particles system */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-60 animate-float-particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          ></div>
        ))}
      </div>

      {/* Inject custom animations */}
      <style dangerouslySetInnerHTML={{ __html: customAnimations }} />

      <div className="container-custom relative z-10">
        {/* Futuristic Section Header */}
        <div className="text-center mb-24">
          {/* Glowing Badge */}
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 backdrop-blur-md rounded-full text-sm font-medium text-cyan-300 mb-8 border border-cyan-400/30 shadow-2xl animate-glow-pulse">
            <div className="w-3 h-3 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full mr-3 animate-pulse"></div>
            <span className="bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent font-semibold">
              Next-Generation Learning Platform
            </span>
          </div>

          {/* Spectacular Title */}
          <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold font-heading mb-8 leading-tight">
            <span className="bg-gradient-to-r from-cyan-300 via-blue-300 to-purple-300 bg-clip-text text-transparent animate-pulse">
              Master English
            </span>
            <br />
            <span className="bg-gradient-to-r from-purple-300 via-pink-300 to-cyan-300 bg-clip-text text-transparent">
              Like Never Before
            </span>
          </h2>

          {/* Enhanced Description */}
          <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed mb-12">
            Experience revolutionary English learning with
            <span className="text-cyan-300 font-semibold"> AI-powered speaking</span>,
            <span className="text-blue-300 font-semibold"> immersive listening</span>,
            <span className="text-purple-300 font-semibold"> interactive reading</span>, and
            <span className="text-pink-300 font-semibold"> intelligent writing</span>
            modules designed for the digital age.
          </p>

          {/* Futuristic Stats */}
          <div className="flex flex-wrap justify-center gap-6 text-center">
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
              <div className="relative bg-slate-800/80 backdrop-blur-md rounded-xl px-6 py-4 border border-cyan-400/20 hover:border-cyan-400/40 transition-all">
                <span className="block text-3xl font-bold bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent">10K+</span>
                <span className="text-sm text-slate-400">Global Learners</span>
              </div>
            </div>
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
              <div className="relative bg-slate-800/80 backdrop-blur-md rounded-xl px-6 py-4 border border-blue-400/20 hover:border-blue-400/40 transition-all">
                <span className="block text-3xl font-bold bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent">98%</span>
                <span className="text-sm text-slate-400">Success Rate</span>
              </div>
            </div>
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
              <div className="relative bg-slate-800/80 backdrop-blur-md rounded-xl px-6 py-4 border border-purple-400/20 hover:border-purple-400/40 transition-all">
                <span className="block text-3xl font-bold bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent">50+</span>
                <span className="text-sm text-slate-400">Countries</span>
              </div>
            </div>
          </div>
        </div>

        {/* Futuristic Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {features.map((feature, index) => {
            const IconComponent = iconMap[feature.icon] || TargetIcon;

            // Dynamic gradient themes for each card
            const gradientThemes = [
              { from: 'from-cyan-500/20', to: 'to-blue-500/20', border: 'border-cyan-400/30', text: 'text-cyan-300', glow: 'from-cyan-500 to-blue-500' },
              { from: 'from-blue-500/20', to: 'to-purple-500/20', border: 'border-blue-400/30', text: 'text-blue-300', glow: 'from-blue-500 to-purple-500' },
              { from: 'from-purple-500/20', to: 'to-pink-500/20', border: 'border-purple-400/30', text: 'text-purple-300', glow: 'from-purple-500 to-pink-500' },
              { from: 'from-pink-500/20', to: 'to-red-500/20', border: 'border-pink-400/30', text: 'text-pink-300', glow: 'from-pink-500 to-red-500' },
              { from: 'from-indigo-500/20', to: 'to-cyan-500/20', border: 'border-indigo-400/30', text: 'text-indigo-300', glow: 'from-indigo-500 to-cyan-500' },
              { from: 'from-emerald-500/20', to: 'to-blue-500/20', border: 'border-emerald-400/30', text: 'text-emerald-300', glow: 'from-emerald-500 to-blue-500' }
            ];

            const theme = gradientThemes[index % gradientThemes.length];

            return (
              <div key={feature.id} className="group relative">
                {/* Glow effect */}
                <div className={`absolute inset-0 bg-gradient-to-r ${theme.glow} rounded-2xl blur-xl opacity-0 group-hover:opacity-30 transition-all duration-700 scale-95 group-hover:scale-105`}></div>

                <Card
                  className={`relative text-center transition-all duration-700 group border-0 bg-gradient-to-br ${theme.from} ${theme.to} backdrop-blur-md hover:scale-105 overflow-hidden min-h-[350px] ${theme.border} border hover:border-opacity-60`}
                  variant="default"
                >
                  {/* Animated background pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-4 right-4 w-32 h-32 border border-current rounded-full animate-spin-slow"></div>
                    <div className="absolute bottom-4 left-4 w-24 h-24 border border-current rounded-lg rotate-45 animate-bounce-slow"></div>
                  </div>

                  {/* Content */}
                  <div className="relative z-10 p-8 h-full flex flex-col justify-center">
                    {/* Futuristic Icon Container */}
                    <div className="mb-8">
                      <div className={`relative w-24 h-24 bg-gradient-to-br ${theme.from} ${theme.to} backdrop-blur-md rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-700 shadow-2xl border ${theme.border} ${theme.text} animate-float`}>
                        <IconComponent />
                        {/* Orbital rings */}
                        <div className="absolute inset-0 rounded-3xl border-2 border-current opacity-20 scale-110 group-hover:scale-125 group-hover:opacity-40 transition-all duration-700 animate-spin-slow"></div>
                        <div className="absolute inset-0 rounded-3xl border border-current opacity-10 scale-125 group-hover:scale-140 transition-all duration-700"></div>
                      </div>
                    </div>

                    {/* Futuristic Title */}
                    <h3 className={`text-xl md:text-2xl font-bold text-white mb-4 group-hover:${theme.text} transition-colors duration-500`}>
                      {feature.title}
                    </h3>

                    {/* Enhanced Description */}
                    <p className="text-slate-300 leading-relaxed text-base group-hover:text-white transition-colors duration-500 mb-6">
                      {feature.description}
                    </p>

                    {/* Progress indicator */}
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-current to-transparent opacity-0 group-hover:opacity-60 transition-opacity duration-500"></div>

                    {/* Corner accents */}
                    <div className="absolute top-0 left-0 w-8 h-8 border-l-2 border-t-2 border-current opacity-20 group-hover:opacity-60 transition-opacity duration-500"></div>
                    <div className="absolute bottom-0 right-0 w-8 h-8 border-r-2 border-b-2 border-current opacity-20 group-hover:opacity-60 transition-opacity duration-500"></div>
                  </div>
                </Card>
              </div>
            );
          })}
        </div>

        {/* Spectacular CTA Section */}
        <div className="text-center mt-24">
          <div className="relative group">
            {/* Glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 rounded-full blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500 scale-110"></div>

            <div className="relative inline-flex items-center gap-3 text-white text-lg bg-gradient-to-r from-slate-800/80 to-slate-900/80 backdrop-blur-md rounded-full px-8 py-4 border border-cyan-400/30 shadow-2xl hover:border-cyan-400/60 transition-all duration-500">
              <span className="bg-gradient-to-r from-cyan-300 to-purple-300 bg-clip-text text-transparent font-semibold">
                Ready to start your English learning journey?
              </span>
              <div className="flex gap-1.5">
                <div className="w-2.5 h-2.5 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full animate-bounce"></div>
                <div className="w-2.5 h-2.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2.5 h-2.5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>

          {/* Additional floating elements */}
          <div className="mt-8 flex justify-center gap-8 opacity-60">
            <div className="w-3 h-3 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
            <div className="w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
            <div className="w-2 h-2 bg-gradient-to-r from-pink-400 to-cyan-400 rounded-full animate-pulse" style={{ animationDelay: '1.5s' }}></div>
            <div className="w-3 h-3 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
          </div>
        </div>
      </div>
    </section>
  );
};
