import React from 'react';
import { Card } from '../ui';
import { features } from '../../data';
import { cn } from '../../utils';

// SVG Icon Components with Colors - mga icons para sa features
const TargetIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="#3B82F6" fillOpacity="0.1" stroke="#3B82F6" strokeWidth="2"/>
    <path d="M9 12l2 2 4-4" stroke="#10B981" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const BookIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="#8B5CF6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" fill="#8B5CF6" fillOpacity="0.1" stroke="#8B5CF6" strokeWidth="2"/>
    <path d="M8 7h8M8 11h6" stroke="#8B5CF6" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

const LightningIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="#F59E0B" stroke="#F59E0B" strokeWidth="2" strokeLinejoin="round"/>
    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="url(#lightning-gradient)"/>
    <defs>
      <linearGradient id="lightning-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FCD34D"/>
        <stop offset="100%" stopColor="#F59E0B"/>
      </linearGradient>
    </defs>
  </svg>
);

const ChartIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="3" y="12" width="4" height="9" fill="#06B6D4" rx="1"/>
    <rect x="10" y="8" width="4" height="13" fill="#3B82F6" rx="1"/>
    <rect x="17" y="4" width="4" height="17" fill="#8B5CF6" rx="1"/>
    <path d="M3 3v18h18" stroke="#64748B" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const UsersIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="9" cy="7" r="4" fill="#EC4899" fillOpacity="0.2" stroke="#EC4899" strokeWidth="2"/>
    <circle cx="15" cy="7" r="4" fill="#06B6D4" fillOpacity="0.2" stroke="#06B6D4" strokeWidth="2"/>
    <path d="M1 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" stroke="#EC4899" strokeWidth="2" strokeLinecap="round"/>
    <path d="M15 21v-2a4 4 0 0 1 4-4h4" stroke="#06B6D4" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const DeviceIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="5" y="2" width="14" height="20" rx="3" fill="url(#device-gradient)" stroke="#6366F1" strokeWidth="2"/>
    <rect x="7" y="5" width="10" height="12" fill="#F8FAFC" rx="1"/>
    <circle cx="12" cy="19" r="1.5" fill="#6366F1"/>
    <defs>
      <linearGradient id="device-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E0E7FF"/>
        <stop offset="100%" stopColor="#C7D2FE"/>
      </linearGradient>
    </defs>
  </svg>
);

// Icon mapping
const iconMap: { [key: string]: React.ComponentType } = {
  '🎯': TargetIcon,
  '📚': BookIcon,
  '⚡': LightningIcon,
  '📊': ChartIcon,
  '👥': UsersIcon,
  '📱': DeviceIcon,
};

interface FeaturesSectionProps {
  className?: string;
}

export const FeaturesSection: React.FC<FeaturesSectionProps> = ({ className }) => {
  return (
    <section id="features" className={cn('relative py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 overflow-hidden', className)}>
      {/* Background Decorative Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full opacity-30 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-100 to-blue-100 rounded-full opacity-30 blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-50 to-pink-50 rounded-full opacity-20 blur-3xl"></div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-2 h-2 bg-blue-400 rounded-full opacity-60 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-purple-400 rounded-full opacity-40 animate-bounce"></div>
        <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-green-400 rounded-full opacity-50 animate-pulse"></div>
        <div className="absolute bottom-20 right-1/3 w-1 h-1 bg-pink-400 rounded-full opacity-60 animate-bounce"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Enhanced Section Header */}
        <div className="text-center mb-20">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full text-sm font-medium text-blue-800 mb-6 border border-blue-200 shadow-sm">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
            Comprehensive Learning Platform
          </div>

          {/* Main Title with Gradient */}
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold font-heading mb-6 leading-tight">
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
              Complete English
            </span>
            <br />
            <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-purple-800 bg-clip-text text-transparent">
              Learning Solution
            </span>
          </h2>

          {/* Enhanced Description */}
          <p className="text-xl md:text-2xl text-secondary-600 max-w-4xl mx-auto leading-relaxed mb-8">
            Master all four essential English skills -
            <span className="font-semibold text-blue-700"> speaking</span>,
            <span className="font-semibold text-purple-700"> listening</span>,
            <span className="font-semibold text-green-700"> reading</span>, and
            <span className="font-semibold text-pink-700"> writing</span> -
            through our comprehensive and scientifically-designed curriculum.
          </p>

          {/* Stats Row */}
          <div className="flex flex-wrap justify-center gap-8 text-center">
            <div className="flex flex-col">
              <span className="text-2xl font-bold text-blue-600">10K+</span>
              <span className="text-sm text-secondary-500">Active Students</span>
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold text-purple-600">95%</span>
              <span className="text-sm text-secondary-500">Success Rate</span>
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold text-green-600">50+</span>
              <span className="text-sm text-secondary-500">Countries</span>
            </div>
          </div>
        </div>

        {/* Enhanced Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {features.map((feature, index) => {
            const IconComponent = iconMap[feature.icon] || TargetIcon;
            const gradients = [
              'from-blue-500 to-purple-600',
              'from-purple-500 to-pink-600',
              'from-green-500 to-blue-600',
              'from-orange-500 to-red-600',
              'from-pink-500 to-purple-600',
              'from-blue-500 to-green-600'
            ];
            const currentGradient = gradients[index % gradients.length];

            return (
              <Card
                key={feature.id}
                className="relative text-center hover:shadow-2xl transition-all duration-500 group border-0 bg-white/80 backdrop-blur-sm hover:bg-white hover:scale-105 overflow-hidden"
                variant="default"
              >
                {/* Card Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${currentGradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>

                {/* Content */}
                <div className="relative z-10 p-8">
                  {/* Enhanced Icon Container */}
                  <div className="mb-8">
                    <div className={`relative w-20 h-20 bg-gradient-to-br ${currentGradient} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg group-hover:shadow-xl`}>
                      <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center">
                        <IconComponent />
                      </div>
                      {/* Floating ring */}
                      <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${currentGradient} opacity-20 scale-110 group-hover:scale-125 transition-transform duration-700`}></div>
                    </div>
                  </div>

                  {/* Enhanced Title */}
                  <h3 className="text-xl md:text-2xl font-bold text-secondary-900 mb-4 group-hover:text-blue-700 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  {/* Enhanced Description */}
                  <p className="text-secondary-600 leading-relaxed text-base group-hover:text-secondary-700 transition-colors duration-300">
                    {feature.description}
                  </p>

                  {/* Hover indicator */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Enhanced CTA Section */}
        <div className="text-center mt-20">
          <div className="inline-flex items-center gap-2 text-secondary-600 text-lg">
            <span>Ready to start your English learning journey?</span>
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
