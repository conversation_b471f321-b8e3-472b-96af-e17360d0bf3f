import React from 'react';
import { Card } from '../ui';
import { features } from '../../data';
import { cn } from '../../utils';

// Professional SVG Icon Components - clean and minimal
const TargetIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const BookIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M8 7h8M8 11h6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

const LightningIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/>
  </svg>
);

const ChartIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="3" y="12" width="4" height="9" fill="currentColor" fillOpacity="0.2" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <rect x="10" y="8" width="4" height="13" fill="currentColor" fillOpacity="0.15" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <rect x="17" y="4" width="4" height="17" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="1.5" rx="1"/>
    <path d="M3 3v18h18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const UsersIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="9" cy="7" r="4" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <circle cx="15" cy="7" r="4" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <path d="M1 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    <path d="M15 21v-2a4 4 0 0 1 4-4h4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const DeviceIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="5" y="2" width="14" height="20" rx="3" fill="currentColor" fillOpacity="0.1" stroke="currentColor" strokeWidth="2"/>
    <rect x="7" y="5" width="10" height="12" fill="white" stroke="currentColor" strokeWidth="1" rx="1"/>
    <circle cx="12" cy="19" r="1.5" fill="currentColor"/>
  </svg>
);

// Icon mapping
const iconMap: { [key: string]: React.ComponentType } = {
  '🎯': TargetIcon,
  '📚': BookIcon,
  '⚡': LightningIcon,
  '📊': ChartIcon,
  '👥': UsersIcon,
  '📱': DeviceIcon,
};

interface FeaturesSectionProps {
  className?: string;
}

export const FeaturesSection: React.FC<FeaturesSectionProps> = ({ className }) => {
  return (
    <section id="features" className={cn('relative py-24 bg-gradient-to-br from-blue-50 via-white to-indigo-50 overflow-hidden', className)}>
      {/* Dynamic Background Pattern */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated gradient orbs */}
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full opacity-30 blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-200 to-purple-300 rounded-full opacity-25 blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full opacity-20 blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>

        {/* Geometric shapes */}
        <div className="absolute top-20 right-1/4 w-32 h-32 border border-blue-200 rounded-lg rotate-45 opacity-20 animate-spin" style={{ animationDuration: '20s' }}></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 border border-indigo-200 rounded-full opacity-25 animate-ping" style={{ animationDuration: '3s' }}></div>

        {/* Grid pattern overlay */}
        <div className="absolute inset-0 opacity-5">
          <div className="h-full w-full" style={{
            backgroundImage: `
              linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}></div>
        </div>
      </div>

      {/* Floating Interactive Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Learning icons floating */}
        <div className="absolute top-32 left-16 opacity-20 animate-bounce" style={{ animationDuration: '3s' }}>
          <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div className="absolute top-48 right-24 opacity-15 animate-bounce" style={{ animationDuration: '4s', animationDelay: '0.5s' }}>
          <svg className="w-6 h-6 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"/>
          </svg>
        </div>
        <div className="absolute bottom-40 left-32 opacity-20 animate-bounce" style={{ animationDuration: '3.5s', animationDelay: '1s' }}>
          <svg className="w-7 h-7 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"/>
          </svg>
        </div>
        <div className="absolute bottom-32 right-16 opacity-15 animate-bounce" style={{ animationDuration: '4.5s', animationDelay: '1.5s' }}>
          <svg className="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
          </svg>
        </div>

        {/* Glowing particles */}
        <div className="absolute top-1/4 left-1/3 w-2 h-2 bg-blue-400 rounded-full opacity-60 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/3 w-1.5 h-1.5 bg-indigo-400 rounded-full opacity-50 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute top-1/2 left-1/5 w-1 h-1 bg-purple-400 rounded-full opacity-40 animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/4 left-2/3 w-2 h-2 bg-blue-300 rounded-full opacity-45 animate-pulse" style={{ animationDelay: '1.5s' }}></div>
      </div>

      {/* Subtle wave pattern at bottom */}
      <div className="absolute bottom-0 left-0 right-0 h-32 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 1200 120" preserveAspectRatio="none">
          <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" fill="currentColor" className="text-blue-100"></path>
        </svg>
      </div>

      <div className="container-custom relative z-10">
        {/* Professional Section Header */}
        <div className="text-center mb-20">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full text-sm font-medium text-blue-700 mb-6 border border-blue-200 shadow-lg">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
            Comprehensive Learning Platform
          </div>

          {/* Main Title - Professional */}
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold font-heading mb-6 leading-tight text-secondary-900">
            Complete English
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Learning Solution
            </span>
          </h2>

          {/* Professional Description */}
          <p className="text-xl md:text-2xl text-secondary-600 max-w-4xl mx-auto leading-relaxed mb-8">
            Master all four essential English skills -
            <span className="font-semibold text-secondary-800"> speaking</span>,
            <span className="font-semibold text-secondary-800"> listening</span>,
            <span className="font-semibold text-secondary-800"> reading</span>, and
            <span className="font-semibold text-secondary-800"> writing</span> -
            through our comprehensive and scientifically-designed curriculum.
          </p>

          {/* Professional Stats Row */}
          <div className="flex flex-wrap justify-center gap-8 text-center">
            <div className="flex flex-col bg-white/60 backdrop-blur-sm rounded-lg px-4 py-3 shadow-lg">
              <span className="text-2xl font-bold text-blue-600">10K+</span>
              <span className="text-sm text-secondary-500">Active Students</span>
            </div>
            <div className="flex flex-col bg-white/60 backdrop-blur-sm rounded-lg px-4 py-3 shadow-lg">
              <span className="text-2xl font-bold text-indigo-600">95%</span>
              <span className="text-sm text-secondary-500">Success Rate</span>
            </div>
            <div className="flex flex-col bg-white/60 backdrop-blur-sm rounded-lg px-4 py-3 shadow-lg">
              <span className="text-2xl font-bold text-purple-600">50+</span>
              <span className="text-sm text-secondary-500">Countries</span>
            </div>
          </div>
        </div>

        {/* Professional Features Grid with Stock Photos */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {features.map((feature, index) => {
            const IconComponent = iconMap[feature.icon] || TargetIcon;

            // Stock photo URLs that correlate with each feature
            const stockPhotos = [
              'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=300&fit=crop&crop=face', // Speaking & Pronunciation - person speaking
              'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500&h=300&fit=crop', // Grammar Mastery - books and study
              'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=500&h=300&fit=crop', // Vocabulary Building - writing/learning
              'https://images.unsplash.com/photo-1484807352052-23338990c6c6?w=500&h=300&fit=crop', // Listening Comprehension - headphones/audio
              'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=500&h=300&fit=crop', // Writing Skills - writing/typing
              'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=500&h=300&fit=crop' // Cultural Context - diverse people/communication
            ];

            const currentPhoto = stockPhotos[index % stockPhotos.length];

            return (
              <Card
                key={feature.id}
                className="relative text-center hover:shadow-2xl transition-all duration-500 group border-0 bg-white/90 backdrop-blur-sm hover:bg-white hover:scale-105 overflow-hidden min-h-[400px]"
                variant="default"
              >
                {/* Stock Photo Background */}
                <div
                  className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-all duration-500 group-hover:scale-110"
                  style={{
                    backgroundImage: `url(${currentPhoto})`,
                  }}
                ></div>

                {/* Photo Overlay for Text Visibility */}
                <div className="absolute inset-0 bg-white/85 group-hover:bg-white/90 transition-all duration-500"></div>

                {/* Gradient Overlay for Better Text Contrast */}
                <div className="absolute inset-0 bg-gradient-to-t from-white/95 via-white/80 to-white/70 group-hover:from-white/98 group-hover:via-white/90 group-hover:to-white/80 transition-all duration-500"></div>

                {/* Content */}
                <div className="relative z-10 p-8 h-full flex flex-col justify-center">
                  {/* Professional Icon Container */}
                  <div className="mb-8">
                    <div className="relative w-20 h-20 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:bg-white transition-all duration-500 shadow-lg group-hover:shadow-xl text-blue-600 border border-blue-100">
                      <IconComponent />
                      {/* Subtle ring effect */}
                      <div className="absolute inset-0 rounded-2xl bg-blue-100 opacity-0 scale-110 group-hover:opacity-50 group-hover:scale-125 transition-all duration-700"></div>
                    </div>
                  </div>

                  {/* Professional Title */}
                  <h3 className="text-xl md:text-2xl font-bold text-secondary-900 mb-4 group-hover:text-blue-700 transition-colors duration-300 drop-shadow-sm">
                    {feature.title}
                  </h3>

                  {/* Professional Description */}
                  <p className="text-secondary-700 leading-relaxed text-base group-hover:text-secondary-800 transition-colors duration-300 drop-shadow-sm">
                    {feature.description}
                  </p>

                  {/* Subtle hover indicator */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Professional CTA Section */}
        <div className="text-center mt-20">
          <div className="inline-flex items-center gap-2 text-secondary-600 text-lg bg-white/60 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg">
            <span>Ready to start your English learning journey?</span>
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
